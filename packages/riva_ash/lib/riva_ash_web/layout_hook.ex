defmodule RivaAshWeb.LayoutHook do
  @moduledoc """
  A module that provides layout handling for LiveViews.
  It ensures that authenticated LiveViews use the authenticated layout.
  """

  defmacro __before_compile__(_env) do
    quote do
      # Override the mount function to set the layout
      defoverridable mount: 3

      def mount(params, session, socket) do
        case super(params, session, socket) do
          {:ok, socket} ->
            socket = Phoenix.LiveView.put_root_layout(socket, {RivaAshWeb.Layouts, :authenticated})
            {:ok, socket}
          other -> other
        end
      end
    end
  end
end
